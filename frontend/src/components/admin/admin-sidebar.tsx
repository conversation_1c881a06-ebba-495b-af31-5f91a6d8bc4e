'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  BarChart3,
  Building,
  CreditCard,
  FileText,
  Lock,
  Settings,
  Shield,
  Users,
  AlertTriangle,
  Gauge,
  Phone,
  ChevronLeft,
  ChevronRight,
  Home,
  Key,
  PhoneCall,
  Hash,
  BellRing,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth/useAuth';
import { useFeatures } from '@/hooks/useFeatures';

interface SidebarItemProps {
  href: string;
  icon: React.ReactNode;
  title: string;
  isActive: boolean;
  isCollapsed: boolean;
}

const SidebarItem = ({ href, icon, title, isActive, isCollapsed }: SidebarItemProps) => {
  return (
    <Link
      href={href}
      className={cn(
        'flex items-center py-2 px-3 rounded-md text-sm font-medium transition-colors',
        isActive
          ? 'bg-primary text-primary-foreground hover:bg-primary/90'
          : 'text-muted-foreground hover:text-foreground hover:bg-muted',
        isCollapsed ? 'justify-center' : ''
      )}
    >
      <span className={cn('mr-2', isCollapsed ? 'mr-0' : '')}>{icon}</span>
      {!isCollapsed && <span>{title}</span>}
    </Link>
  );
};

export default function AdminSidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { profile } = useAuth();
  const { hasFeature } = useFeatures();

  // Check if user has access to voice features
  const hasVoiceAccess = profile?.role === 'partner' && hasFeature('voice_intake');

  const sidebarItems = [
    {
      href: '/admin',
      icon: <Home size={20} />,
      title: 'Dashboard',
    },
    {
      href: '/admin/tenants',
      icon: <Building size={20} />,
      title: 'Tenants',
    },
    {
      href: '/admin/users',
      icon: <Users size={20} />,
      title: 'Users',
    },
    {
      href: '/admin/subscriptions',
      icon: <CreditCard size={20} />,
      title: 'Subscriptions',
    },
    {
      href: '/admin/usage',
      icon: <Gauge size={20} />,
      title: 'Usage',
    },
    {
      href: '/admin/voice-agents',
      icon: <Phone size={20} />,
      title: 'Voice Agents',
    },
    // Voice Receptionist section (only for partners with voice_intake feature)
    ...(hasVoiceAccess ? [
      {
        href: '/admin/voice',
        icon: <Phone size={20} />,
        title: 'Voice Receptionist',
      },
      {
        href: '/admin/voice/calls',
        icon: <PhoneCall size={20} />,
        title: 'Call Logs',
      },
      {
        href: '/admin/voice/numbers',
        icon: <Hash size={20} />,
        title: 'Phone Numbers',
      },
      {
        href: '/admin/voice/notifications',
        icon: <BellRing size={20} />,
        title: 'Failed Notifications',
      },
    ] : []),
    // Security section
    {
      href: '/admin/security',
      icon: <Shield size={20} />,
      title: 'Security',
    },
    {
      href: '/admin/security/alerts',
      icon: <AlertTriangle size={20} />,
      title: 'Security Alerts',
    },
    {
      href: '/admin/security/anomalies',
      icon: <AlertTriangle size={20} />,
      title: 'Anomalies',
    },
    {
      href: '/admin/security/tokens',
      icon: <Key size={20} />,
      title: 'Token Management',
    },
    {
      href: '/admin/security/settings',
      icon: <Settings size={20} />,
      title: 'Security Settings',
    },
    {
      href: '/admin/logs',
      icon: <FileText size={20} />,
      title: 'Logs',
    },
    {
      href: '/admin/analytics',
      icon: <BarChart3 size={20} />,
      title: 'Analytics',
    },
    {
      href: '/admin/settings',
      icon: <Settings size={20} />,
      title: 'Settings',
    },
  ];

  return (
    <div
      className={cn(
        'bg-card border-r flex flex-col h-full transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      <div className="p-4 flex items-center justify-between border-b">
        {!isCollapsed && (
          <div className="font-bold text-lg">
            Admin Dashboard
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="ml-auto"
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </Button>
      </div>
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="px-2 space-y-1">
          {sidebarItems.map((item) => (
            <SidebarItem
              key={item.href}
              href={item.href}
              icon={item.icon}
              title={item.title}
              isActive={pathname === item.href}
              isCollapsed={isCollapsed}
            />
          ))}
        </nav>
      </div>
      <div className="p-4 border-t">
        <SidebarItem
          href="/dashboard"
          icon={<Lock size={20} />}
          title="Exit Admin"
          isActive={false}
          isCollapsed={isCollapsed}
        />
      </div>
    </div>
  );
}
