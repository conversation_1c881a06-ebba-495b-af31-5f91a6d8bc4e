/**
 * Activity Logger
 * Records user activities in Supabase for later analysis and insight generation
 */

import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import { setUserLastActive } from './redis/user-activity';

// Initialize Supabase
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// Activity interface for structured logging
interface Activity {
  id?: string;
  userId: string;
  type: string;
  description: string;
  metadata?: Record<string, unknown>;
  caseId?: string;
  documentId?: string;
  clientId?: string;
  timestamp?: string;
}

/**
 * Log an activity to Supabase
 * Also updates Redis last active timestamp for the user
 *
 * @param activity Activity object to log
 * @returns Promise resolving to activity ID
 */
export async function logActivity(activity: Activity): Promise<string> {
  try {
    // Prepare activity data
    const activityId = activity.id || `act_${uuidv4()}`;
    const timestamp = activity.timestamp || new Date().toISOString();

    // Format metadata as JSON string for Supabase
    const metadata = activity.metadata
      ? JSON.stringify(activity.metadata)
      : null;

    // Insert into Supabase
    const { error } = await supabase
      .schema('tenants')
      .from('activities')
      .insert({
        id: activityId,
        user_id: activity.userId,
        type: activity.type,
        description: activity.description,
        metadata,
        case_id: activity.caseId,
        document_id: activity.documentId,
        client_id: activity.clientId,
        created_at: timestamp
      });

    if (error) {
      console.error('Error logging activity:', error);
      throw error;
    }

    // Update Redis with last activity time
    await setUserLastActive(activity.userId, activity.type, timestamp);

    // For specific activity types, update the app_open timestamp as well
    const importantActivities = [
      'page_view', 'click', 'search', 'document_open',
      'case_view', 'client_view', 'insight_view'
    ];

    if (importantActivities.includes(activity.type)) {
      await setUserLastActive(activity.userId, 'app_open', timestamp);
    }

    return activityId;
  } catch (error) {
    console.error('Failed to log activity:', error);
    return `error_${uuidv4()}`;
  }
}

/**
 * Get recent activities for a user
 *
 * @param userId User ID
 * @param limit Maximum number of activities to return
 * @param activityTypes Optional filter for specific activity types
 * @returns Promise resolving to array of activities
 */
export async function getRecentActivities(
  userId: string,
  limit: number = 20,
  activityTypes?: string[]
): Promise<Activity[]> {
  try {
    let query = supabase
      .schema('tenants')
      .from('activities')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply activity type filter if provided
    if (activityTypes && activityTypes.length > 0) {
      query = query.in('type', activityTypes);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching activities:', error);
      return [];
    }

    return data.map(item => ({
      id: item.id,
      userId: item.user_id,
      type: item.type,
      description: item.description,
      metadata: item.metadata ? JSON.parse(item.metadata) : {},
      caseId: item.case_id,
      documentId: item.document_id,
      clientId: item.client_id,
      timestamp: item.created_at
    }));
  } catch (error) {
    console.error('Failed to get recent activities:', error);
    return [];
  }
}

/**
 * Get activities related to a specific entity
 *
 * @param entityType Type of entity ('case', 'document', 'client')
 * @param entityId Entity ID
 * @param limit Maximum number of activities to return
 * @returns Promise resolving to array of activities
 */
export async function getEntityActivities(
  entityType: 'case' | 'document' | 'client',
  entityId: string,
  limit: number = 20
): Promise<Activity[]> {
  try {
    const columnMap = {
      case: 'case_id',
      document: 'document_id',
      client: 'client_id'
    };

    const column = columnMap[entityType];

    const { data, error } = await supabase
      .schema('tenants')
      .from('activities')
      .select('*')
      .eq(column, entityId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error(`Error fetching ${entityType} activities:`, error);
      return [];
    }

    return data.map(item => ({
      id: item.id,
      userId: item.user_id,
      type: item.type,
      description: item.description,
      metadata: item.metadata ? JSON.parse(item.metadata) : {},
      caseId: item.case_id,
      documentId: item.document_id,
      clientId: item.client_id,
      timestamp: item.created_at
    }));
  } catch (error) {
    console.error(`Failed to get ${entityType} activities:`, error);
    return [];
  }
}
