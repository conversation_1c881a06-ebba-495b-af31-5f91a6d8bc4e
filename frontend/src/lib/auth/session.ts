/**
 * Session Management (Client-Safe)
 * Handles client-side session utilities
 * Note: Server-side functions are in session-server.ts
 */

import { Session } from '@supabase/supabase-js';
import { AuthUser, UserRole, TenantClaims, AuthRouteHandler, SessionExt } from './types';
import { parseJwtPayload, type JwtPayload } from '../supabase/client';

/**
 * Gets the current server session using unified session approach
 * Note: This is a client-safe stub. Use session-server.ts for actual implementation.
 *
 * @returns The current session or null if not authenticated
 */
export async function getServerSession(): Promise<Session | null> {
  throw new Error('getServerSession is server-side only. Import from @/lib/auth/server-exports instead.');
}

/**
 * Gets the unified session directly
 * Note: This is a client-safe stub. Use session-server.ts for actual implementation.
 *
 * @returns The unified session or null if not authenticated
 */
export async function getUnifiedServerSession(): Promise<any | null> {
  throw new Error('getUnifiedServerSession is server-side only. Import from @/lib/auth/server-exports instead.');
}

/**
 * Gets the current authenticated user
 * Note: This is a client-safe stub. Use session-server.ts for actual implementation.
 *
 * @returns The current user or null if not authenticated
 */
export async function getUser(): Promise<AuthUser | null> {
  throw new Error('getUser is server-side only. Import from @/lib/auth/server-exports instead.');
}

/**
 * Requires authentication and returns the user
 * Note: This is a client-safe stub. Use session-server.ts for actual implementation.
 *
 * @param allowedRoles Optional array of allowed roles
 * @returns The authenticated user
 * @throws Error if not authenticated or insufficient permissions
 */
export async function requireAuth(allowedRoles?: UserRole[]): Promise<AuthUser> {
  throw new Error('requireAuth is server-side only. Import from @/lib/auth/server-exports instead.');
}

/**
 * Creates an AuthUser object from a Supabase session (Client-Safe Version)
 * Handles JWT parsing without server-side dependencies
 *
 * @param session The Supabase session
 * @returns The AuthUser object or null if session is invalid
 */
export function createAuthUserFromSession(session: Session | null): AuthUser | null {
  if (!session?.user || !session.access_token) {
    return null;
  }

  const userFromSession = session.user;
  const token = session.access_token;
  let role: UserRole = UserRole.Client; // Use Client as default
  let tenantId: string | null = null;
  let email: string | undefined | null = userFromSession.email;

  try {
    const decoded: JwtPayload | null = parseJwtPayload(token);
    if (decoded) {
      // Validate required claims from JWT
      if (!decoded.sub) throw new Error('JWT missing required claim: sub');
      if (!decoded.email) throw new Error('JWT missing required claim: email');
      if (!decoded.role) throw new Error('JWT missing required claim: role');

      const roleFromToken = decoded.role as string;
      if (isValidUserRole(roleFromToken)) {
        role = roleFromToken as UserRole;
      }
      tenantId = decoded.tenant_id ?? null;
      email = decoded.email;

      // Construct the AuthUser object
      const authUser: AuthUser = {
        id: decoded.sub,
        email: email,
        role: role,
        tenantId: tenantId,
        metadata: userFromSession.user_metadata ?? {},
      };

      return authUser;
    } else {
      return null;
    }
  } catch (error: any) {
    console.error('Error processing session/JWT:', error);
    return null;
  }
}

/**
 * Wraps a route handler with authentication and typed database access
 * Note: This is a client-safe stub. Use session-server.ts for actual implementation.
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withAuth(handler: AuthRouteHandler): any {
  throw new Error('withAuth is server-side only. Import from @/lib/auth/server-exports instead.');
}

/**
 * Wraps a route handler with service role access
 * Note: This is a client-safe stub. Use session-server.ts for actual implementation.
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withServiceRole(handler: any): any {
  throw new Error('withServiceRole is server-side only. Import from @/lib/auth/server-exports instead.');
}

// Helper function to check if a role is valid (imported from types)
function isValidUserRole(role: unknown): role is UserRole {
  return typeof role === 'string' &&
    ['authenticated', 'superadmin', 'partner', 'attorney', 'paralegal', 'staff', 'client', 'admin'].includes(role as string);
}
