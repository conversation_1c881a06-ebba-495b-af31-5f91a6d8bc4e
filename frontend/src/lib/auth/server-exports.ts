/**
 * Server-only Authentication Exports
 * 
 * This file exports server-side authentication functions that use next/headers
 * Import from '@/lib/auth/server-exports' in server components and API routes
 */

// Server client creation (server-only)
export {
  createClient,
  createServiceClient,
  createServerClientForUser
} from './server';

// Server session management
export {
  withAuth,
  withServiceRole,
  getServerSession,
  getUnifiedServerSession,
  getUser,
  requireAuth,
  createAuthUserFromSession
} from './session-server';

// Unified session management (server-only)
export * from './getUnifiedSession';

// Server permissions (client-safe functions)
export {
  hasRole,
  isSuperAdmin,
  isAuthenticated,
  checkPermission,
  getUserPermissions,
  canAccessTenant,
  hasRoleLegacy,
  isAuthenticatedLegacy
} from './permissions';

// Server permissions (server-only functions)
export {
  hasRoleUnified,
  isAuthenticatedUnified,
  getCurrentUserRoles
} from './permissions-server';

// Types
export {
  User<PERSON>ole,
  SUPER_ADMIN_EMAILS,
  isV<PERSON>d<PERSON>ser<PERSON><PERSON>,
  type AuthUser,
  type TenantClaims,
  type <PERSON>th<PERSON>out<PERSON><PERSON><PERSON><PERSON>,
  type AuthR<PERSON>ult,
  type SessionExt,
  type SuperAdminEmail
} from './types';
