/**
 * Authentication Module - Main Entry Point
 *
 * This is the single entry point for all authentication functionality.
 * Import everything you need from '@/lib/auth'
 */

// === CORE MODULES ===
// Client creation and management (browser only)
export * from './client';

// Session management and route protection
export * from './session';

// Note: Unified session management is server-only, import from './server-exports' for server use

// Permissions and role checking
export * from './permissions';

// JWT utilities and verification
export * from './jwt';

// Type definitions
export * from './types';

// === LEGACY COMPONENTS (keep for now) ===
// React components and hooks
export { AuthProvider } from './AuthProvider';
export { AuthContext, useAuth } from './useAuth';
export { useRequireAuth } from './useRequireAuth';
export { useAuthToken } from './useAuthToken';
export { useRequireRole } from './useRequireRole';

// === MAIN EXPORTS FOR COMMON USE ===
// Re-export the most commonly used functions for convenience

// Client creation (browser)
export {
  createBrowserSupabaseClient,
  supabaseClient
} from './client';

// Session management (client-safe exports only)
// Note: Server-side session functions are in './server-exports'

// Permissions (client-safe only)
export {
  hasRole,
  isSuperAdmin,
  isAuthenticated,
  checkPermission,
  getUserPermissions,
  canAccessTenant,
  hasRoleLegacy,
  isAuthenticatedLegacy
} from './permissions';

// JWT utilities
export {
  verifyJwt,
  verifyJWT,
  debugJwtClaims,
  parseClaims,
  extractTenantId,
  extractRole,
  isTokenExpired,
  getTokenTimeRemaining
} from './jwt';

// Types
export {
  UserRole,
  SUPER_ADMIN_EMAILS,
  isValidUserRole,
  type AuthUser,
  type TenantClaims,
  type AuthRouteHandler,
  type AuthResult,
  type SessionExt,
  type SuperAdminEmail
} from './types';
