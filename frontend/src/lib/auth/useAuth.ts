'use client'

/**
 * Core authentication hooks
 */
import { createContext, useContext } from 'react';
import { AuthContextType } from './types';

// Create the auth context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
