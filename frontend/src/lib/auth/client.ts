/**
 * Supabase Browser Client Factory
 * Client-side client creation for authentication and database operations
 */

import { createBrowserClient } from '@supabase/ssr';
import { Database } from '../supabase/database.types';
import { SupabaseClient } from '@supabase/supabase-js';



/**
 * Creates a browser client for client-side operations
 * Used in React components and client-side code
 *
 * @returns A Supabase client for browser operations
 */
export function createBrowserSupabaseClient(): SupabaseClient<Database> {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

/**
 * Named export for the browser client (for consistency)
 */
export const supabaseClient = createBrowserSupabaseClient();
