/**
 * Tests for Unified Session Helper
 * 
 * These tests verify that the unified session helper correctly switches between
 * Supabase Auth and legacy cookie/JWT authentication based on the feature flag.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getUnifiedSession, getUserRoles, isSessionValid } from '../getUnifiedSession';
import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies';

// Mock the Supabase client
vi.mock('@/lib/supabaseClient', () => ({
  createServerClientForUser: vi.fn(() => ({
    auth: {
      getSession: vi.fn(),
    },
  })),
}));

// Mock JWT parsing
vi.mock('@/lib/supabase/client', () => ({
  parseJwtPayload: vi.fn(),
}));

// Mock environment variables
const mockEnv = {
  NEXT_PUBLIC_USE_SUPABASE_AUTH: 'false',
};

describe('getUnifiedSession', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variable
    process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH = 'false';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('when USE_SUPABASE_AUTH=false (legacy mode)', () => {
    beforeEach(() => {
      process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH = 'false';
    });

    it('should return legacy session from cookies', async () => {
      const mockCookies = {
        get: vi.fn((name: string) => {
          if (name === 'sb-anwefmklplkjxkmzpnva-auth-token') {
            return {
              value: JSON.stringify({
                access_token: 'mock-jwt-token',
                refresh_token: 'mock-refresh-token',
              }),
            };
          }
          return undefined;
        }),
      } as unknown as ReadonlyRequestCookies;

      // Mock JWT parsing
      const { parseJwtPayload } = await import('@/lib/supabase/client');
      vi.mocked(parseJwtPayload).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'attorney',
        tenant_id: 'tenant-123',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
      });

      const session = await getUnifiedSession(mockCookies);

      expect(session).toEqual({
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          app_metadata: {
            roles: ['attorney'],
            tenant_id: 'tenant-123',
          },
          user_metadata: {},
        },
        access_token: 'mock-jwt-token',
        refresh_token: 'mock-refresh-token',
        expires_at: expect.any(Number),
        expires_in: expect.any(Number),
        token_type: 'bearer',
        tenant_id: 'tenant-123',
        role: 'attorney',
        source: 'legacy',
      });
    });

    it('should return null when no cookies are present', async () => {
      const mockCookies = {
        get: vi.fn(() => undefined),
      } as unknown as ReadonlyRequestCookies;

      const session = await getUnifiedSession(mockCookies);

      expect(session).toBeNull();
    });

    it('should return null when JWT is expired', async () => {
      const mockCookies = {
        get: vi.fn((name: string) => {
          if (name === 'sb-anwefmklplkjxkmzpnva-auth-token') {
            return {
              value: JSON.stringify({
                access_token: 'expired-jwt-token',
              }),
            };
          }
          return undefined;
        }),
      } as unknown as ReadonlyRequestCookies;

      // Mock JWT parsing with expired token
      const { parseJwtPayload } = await import('@/lib/supabase/client');
      vi.mocked(parseJwtPayload).mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'attorney',
        tenant_id: 'tenant-123',
        exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago (expired)
      });

      const session = await getUnifiedSession(mockCookies);

      expect(session).toBeNull();
    });
  });

  describe('when USE_SUPABASE_AUTH=true (Supabase mode)', () => {
    beforeEach(() => {
      process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH = 'true';
    });

    it('should return Supabase session when available', async () => {
      const mockSupabaseSession = {
        user: {
          id: 'user-456',
          email: '<EMAIL>',
          app_metadata: { roles: ['partner'] },
          user_metadata: {},
        },
        access_token: 'supabase-jwt-token',
        refresh_token: 'supabase-refresh-token',
        expires_at: Math.floor(Date.now() / 1000) + 3600,
        expires_in: 3600,
        token_type: 'bearer',
      };

      const { createServerClientForUser } = await import('@/lib/auth/server');
      const mockSupabase = {
        auth: {
          getSession: vi.fn().mockResolvedValue({
            data: { session: mockSupabaseSession },
            error: null,
          }),
        },
      };
      vi.mocked(createServerClientForUser).mockReturnValue(mockSupabase as any);

      // Mock JWT parsing
      const { parseJwtPayload } = await import('@/lib/supabase/client');
      vi.mocked(parseJwtPayload).mockReturnValue({
        sub: 'user-456',
        email: '<EMAIL>',
        role: 'partner',
        tenant_id: 'tenant-456',
        exp: Math.floor(Date.now() / 1000) + 3600,
      });

      const session = await getUnifiedSession();

      expect(session).toEqual({
        user: {
          id: 'user-456',
          email: '<EMAIL>',
          app_metadata: { roles: ['partner'] },
          user_metadata: {},
        },
        access_token: 'supabase-jwt-token',
        refresh_token: 'supabase-refresh-token',
        expires_at: expect.any(Number),
        expires_in: 3600,
        token_type: 'bearer',
        tenant_id: 'tenant-456',
        role: 'partner',
        source: 'supabase',
      });
    });

    it('should fallback to legacy when Supabase session fails', async () => {
      const { createServerClientForUser } = await import('@/lib/auth/server');
      const mockSupabase = {
        auth: {
          getSession: vi.fn().mockResolvedValue({
            data: { session: null },
            error: new Error('Session not found'),
          }),
        },
      };
      vi.mocked(createServerClientForUser).mockReturnValue(mockSupabase as any);

      const mockCookies = {
        get: vi.fn((name: string) => {
          if (name === 'sb-anwefmklplkjxkmzpnva-auth-token') {
            return {
              value: JSON.stringify({
                access_token: 'fallback-jwt-token',
              }),
            };
          }
          return undefined;
        }),
      } as unknown as ReadonlyRequestCookies;

      // Mock JWT parsing
      const { parseJwtPayload } = await import('@/lib/supabase/client');
      vi.mocked(parseJwtPayload).mockReturnValue({
        sub: 'user-789',
        email: '<EMAIL>',
        role: 'staff',
        tenant_id: 'tenant-789',
        exp: Math.floor(Date.now() / 1000) + 3600,
      });

      const session = await getUnifiedSession(mockCookies);

      expect(session?.source).toBe('legacy');
      expect(session?.user.id).toBe('user-789');
    });
  });
});

describe('getUserRoles', () => {
  it('should extract roles from app_metadata', () => {
    const session = {
      user: {
        id: 'user-123',
        app_metadata: {
          roles: ['attorney', 'partner'],
        },
      },
      access_token: 'token',
      source: 'supabase' as const,
    };

    const roles = getUserRoles(session);
    expect(roles).toEqual(['attorney', 'partner']);
  });

  it('should extract role from legacy role field', () => {
    const session = {
      user: {
        id: 'user-123',
        app_metadata: {},
      },
      access_token: 'token',
      role: 'staff',
      source: 'legacy' as const,
    };

    const roles = getUserRoles(session);
    expect(roles).toEqual(['staff']);
  });

  it('should return empty array for null session', () => {
    const roles = getUserRoles(null);
    expect(roles).toEqual([]);
  });
});

describe('isSessionValid', () => {
  it('should return true for valid session', () => {
    const session = {
      user: { id: 'user-123' },
      access_token: 'token',
      expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
      source: 'supabase' as const,
    };

    expect(isSessionValid(session)).toBe(true);
  });

  it('should return false for expired session', () => {
    const session = {
      user: { id: 'user-123' },
      access_token: 'token',
      expires_at: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
      source: 'supabase' as const,
    };

    expect(isSessionValid(session)).toBe(false);
  });

  it('should return false for null session', () => {
    expect(isSessionValid(null)).toBe(false);
  });

  it('should return true for session without expiration', () => {
    const session = {
      user: { id: 'user-123' },
      access_token: 'token',
      source: 'legacy' as const,
    };

    expect(isSessionValid(session)).toBe(true);
  });
});
