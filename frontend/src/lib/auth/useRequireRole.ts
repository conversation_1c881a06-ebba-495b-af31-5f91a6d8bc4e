'use client'

/**
 * Hook to require a specific role
 */
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './useAuth';

export const useRequireRole = (allowedRoles: string[]) => {
  const { profile, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && profile) {
      if (!allowedRoles.includes(profile.role)) {
        router.push('/unauthorized');
      }
    }
  }, [profile, isLoading, isAuthenticated, allowedRoles, router]);

  return { isLoading, isAuthorized: profile && allowedRoles.includes(profile.role) };
};
