import { SupabaseClient, PostgrestResponse } from '@supabase/supabase-js';
import { z } from 'zod';
import { CaseStatus } from '@/types/domain/tenants/Case';

// Case validation schema
export const CaseSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().nullable().optional(),
  sensitive: z.boolean().optional().default(false),
  client_id: z.string().uuid('Invalid client ID'),
  metadata: z.record(z.unknown()).optional().default(() => ({})),
  status: z.enum(['pending', 'active', 'closed', 'rejected']).optional().default('pending'),
  rejection_reason: z.string().nullable().optional(),
});

// Case update schema (all fields optional)
export const UpdateCaseSchema = CaseSchema.partial();

// Case history types
export type CaseChangeType = 'created' | 'updated' | 'status_changed' | 'deleted';

export interface CaseHistoryEntry {
  case_id: string;
  tenant_id: string;
  changed_by: string;
  change_type: CaseChangeType;
  previous_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  created_at?: string;
}

// Type for the client data fetched by fetchClientsByIds
interface FetchedClientSummary {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
}

// Define the structure for the data returned by getAllCases
interface FetchedCaseSummary {
  id: string;
  title: string;
  description: string | null;
  status: string;
  sensitive: boolean;
  created_at: string;
  updated_at: string;
  metadata: Record<string, any>; // Keep as any for now
  rejection_reason: string | null;
  client_id: string | null;
  created_by: string | null;
  creator: {
    id: string;
    email: string | null;
    first_name: string | null;
    last_name: string | null;
  } | null;
}

export class CaseService {
  private supabase: SupabaseClient;
  private tenantId: string;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
  }

  /**
   * Fetch all cases for the current tenant with optional filtering and pagination
   */
  async getAll(options: {
    page?: number;
    limit?: number;
    status?: CaseStatus;
    client_id?: string;
    searchTerm?: string;
  } = {}) {
    const {
      page = 1,
      limit = 10,
      status,
      client_id,
      searchTerm
    } = options;

    let query = this.supabase
      .schema('tenants')
      .from('cases')
      .select(`
        id,
        title,
        description,
        status,
        sensitive,
        created_at,
        updated_at,
        metadata,
        rejection_reason,
        client_id,
        created_by,
        creator:users!cases_created_by_fkey(id, email, first_name, last_name)
      `, { count: 'exact' })
      .eq('tenant_id', this.tenantId);

    // Apply filters
    if (status) query = query.eq('status', status);
    if (client_id) query = query.eq('client_id', client_id);

    // Apply search if provided
    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to).order('created_at', { ascending: false });

    const { data, error, count } = await query as PostgrestResponse<FetchedCaseSummary>;

    if (error) {
      console.error('Error fetching cases:', error);
      throw error;
    }

    // If we have cases, fetch client information for each case
    if (data && data.length > 0) {
      // Extract unique client IDs
      const clientIds = [...new Set(data.filter(c => c.client_id).map(c => c.client_id))] as string[];

      // Fetch clients in bulk if we have any client IDs
      const clients = clientIds.length > 0
        ? await this.fetchClientsByIds(clientIds)
        : [];

      // Map clients by ID for easy lookup
      const clientMap = clients.reduce((map: Record<string, FetchedClientSummary>, client) => {
        map[client.id] = client;
        return map;
      }, {} as Record<string, FetchedClientSummary>);

      // Add client information to each case
      const processedCases = data.map((caseData: FetchedCaseSummary) => {
        const clientData = caseData.client_id ? clientMap[caseData.client_id] : null;

        return {
          ...caseData,
          client: clientData,
          client_name: clientData
            ? `${clientData.first_name || ''} ${clientData.last_name || ''}`.trim()
            : 'Unknown Client',
          created_at: caseData.created_at ? new Date(caseData.created_at).toISOString() : null,
          updated_at: caseData.updated_at ? new Date(caseData.updated_at).toISOString() : null,
        };
      });

      return {
        cases: processedCases,
        page,
        limit,
        total: count || 0,
        totalPages: count ? Math.ceil(count / limit) : 0,
      };
    }

    // No cases found
    return {
      cases: [],
      page,
      limit,
      total: count || 0,
      totalPages: count ? Math.ceil(count / limit) : 0,
    };
  }

  /**
   * Get a single case by ID with client information
   */
  async getById(id: string) {
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('cases')
      .select(`
        id,
        title,
        description,
        status,
        sensitive,
        created_at,
        updated_at,
        metadata,
        rejection_reason,
        client_id,
        created_by,
        creator:users!cases_created_by_fkey(id, email, first_name, last_name)
      `)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Case not found');
      }
      console.error('Error fetching case:', error);
      throw error;
    }

    // Fetch client information
    let clientData: FetchedClientSummary | null = null;
    if (data.client_id) {
      const { data: client, error: clientError } = await this.supabase
        .schema('tenants')
        .from('clients')
        .select('id, first_name, last_name, email, status, client_type')
        .eq('id', data.client_id)
        .single();

      if (!clientError) {
        clientData = client as FetchedClientSummary;
      }
    }

    return {
      ...data,
      client: clientData,
      client_name: clientData
        ? `${clientData.first_name || ''} ${clientData.last_name || ''}`.trim()
        : 'Unknown Client',
      created_at: data.created_at ? new Date(data.created_at).toISOString() : null,
      updated_at: data.updated_at ? new Date(data.updated_at).toISOString() : null,
    };
  }

  /**
   * Create a new case
   */
  async create(caseData: z.infer<typeof CaseSchema>, userId: string) {
    // Validate case data
    const validatedData = CaseSchema.parse(caseData);

    // Create new case
    const newCase = {
      ...validatedData,
      tenant_id: this.tenantId,
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.supabase
      .schema('tenants')
      .from('cases')
      .insert(newCase)
      .select(`
        id,
        title,
        description,
        status,
        sensitive,
        created_at,
        updated_at,
        metadata,
        rejection_reason,
        client_id,
        created_by,
        creator:users!cases_created_by_fkey(id, email, first_name, last_name)
      `)
      .single();

    if (error) {
      console.error('Error creating case:', error);
      throw error;
    }

    // Fetch client information
    let clientData: FetchedClientSummary | null = null;
    if (data.client_id) {
      const { data: client, error: clientError } = await this.supabase
        .schema('tenants')
        .from('clients')
        .select('id, first_name, last_name, email')
        .eq('id', data.client_id)
        .single();

      if (!clientError) {
        clientData = client as FetchedClientSummary;
      }
    }

    const processedCase = {
      ...data,
      client: clientData,
      client_name: clientData
        ? `${clientData.first_name || ''} ${clientData.last_name || ''}`.trim()
        : 'Unknown Client',
    };

    // Log history entry
    await this.logHistory({
      case_id: data.id,
      tenant_id: this.tenantId,
      changed_by: userId,
      change_type: 'created',
      new_values: data
    });

    return processedCase;
  }

  /**
   * Update an existing case
   */
  async update(id: string, updateData: z.infer<typeof UpdateCaseSchema>, userId: string) {
    // Validate update data
    const validatedData = UpdateCaseSchema.parse(updateData);

    // Get existing case for history
    const { data: existingCase, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('cases')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        throw new Error('Case not found');
      }
      console.error('Error fetching case for update:', fetchError);
      throw fetchError;
    }

    // Check if status is changing
    const isStatusChange = validatedData.status && validatedData.status !== existingCase.status;

    // Prepare update payload
    const payload = {
      ...validatedData,
      updated_by: userId,
      updated_at: new Date().toISOString(),
    };

    // Update the case
    const { data: updatedCase, error: updateError } = await this.supabase
      .schema('tenants')
      .from('cases')
      .update(payload)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .select(`
        id,
        title,
        description,
        status,
        sensitive,
        created_at,
        updated_at,
        metadata,
        rejection_reason,
        client_id,
        created_by,
        creator:users!cases_created_by_fkey(id, email, first_name, last_name)
      `)
      .single();

    if (updateError) {
      console.error('Error updating case:', updateError);
      throw updateError;
    }

    // Fetch client information
    let clientData: FetchedClientSummary | null = null;
    if (updatedCase.client_id) {
      const { data: client, error: clientError } = await this.supabase
        .schema('tenants')
        .from('clients')
        .select('id, first_name, last_name, email')
        .eq('id', updatedCase.client_id)
        .single();

      if (!clientError) {
        clientData = client as FetchedClientSummary;
      }
    }

    const processedCase = {
      ...updatedCase,
      client: clientData,
      client_name: clientData
        ? `${clientData.first_name || ''} ${clientData.last_name || ''}`.trim()
        : 'Unknown Client',
    };

    // Log history entry
    await this.logHistory({
      case_id: id,
      tenant_id: this.tenantId,
      changed_by: userId,
      change_type: isStatusChange ? 'status_changed' : 'updated',
      previous_values: existingCase,
      new_values: updatedCase
    });

    return processedCase;
  }

  /**
   * Delete a case
   */
  async delete(id: string, userId: string) {
    // Get case to delete for history
    const { data: caseToDelete, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('cases')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return false; // Case not found
      }
      console.error('Error fetching case for deletion:', fetchError);
      throw fetchError;
    }

    // Delete the case
    const { error: deleteError } = await this.supabase
      .schema('tenants')
      .from('cases')
      .delete()
      .eq('id', id)
      .eq('tenant_id', this.tenantId);

    if (deleteError) {
      console.error('Error deleting case:', deleteError);
      throw deleteError;
    }

    // Log history entry
    await this.logHistory({
      case_id: id,
      tenant_id: this.tenantId,
      changed_by: userId,
      change_type: 'deleted',
      previous_values: caseToDelete
    });

    return true;
  }

  /**
   * Get cases by client ID
   */
  async getByClientId(clientId: string, options: { page?: number; limit?: number } = {}) {
    const { page = 1, limit = 10 } = options;

    let query = this.supabase
      .schema('tenants')
      .from('cases')
      .select(`
        id,
        title,
        description,
        status,
        sensitive,
        created_at,
        updated_at,
        metadata,
        rejection_reason,
        client_id,
        created_by,
        creator:users!cases_created_by_fkey(id, email, first_name, last_name)
      `, { count: 'exact' })
      .eq('tenant_id', this.tenantId)
      .eq('client_id', clientId);

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to).order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching cases by client ID:', error);
      throw error;
    }

    return {
      cases: data || [],
      page,
      limit,
      total: count || 0,
      totalPages: count ? Math.ceil(count / limit) : 0,
    };
  }

  /**
   * Utility method to fetch clients by IDs in a single query
   */
  private async fetchClientsByIds(clientIds: string[]): Promise<FetchedClientSummary[]> {
    if (!clientIds.length) return [];

    const { data, error } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('id, first_name, last_name, email, status, client_type')
      .in('id', clientIds)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error fetching clients by IDs:', error);
      return [];
    }

    return (data as FetchedClientSummary[]) || []; // Assert return type
  }

  /**
   * Log a history entry for case changes
   */
  private async logHistory(historyEntry: CaseHistoryEntry) {
    const { error } = await this.supabase
      .schema('tenants')
      .from('case_history')
      .insert({
        ...historyEntry,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error logging case history:', error);
      // Don't throw error for history logging failures
      // to avoid disrupting the main transaction
    }
  }
}
