/**
 * Insights Service
 *
 * This service handles generating and retrieving insights based on ML-prioritized activities.
 * It works in tandem with the activity-priority-service to provide comprehensive activity management.
 */
import { Activity, ActivityFeatures } from '../ml/features';
import { PriorityLevel } from '../ml/modelInference';
import { activityPriorityService } from './activity-priority-service';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Insight interface
export interface Insight {
  id: string;
  tenant_id: string;
  user_id: string | null;
  title: string;
  description: string;
  priority: PriorityLevel;
  source_activities: string[];
  actions: InsightAction[];
  metadata: Record<string, unknown>;
  created_at: string;
}

// Insight action interface
export interface InsightAction {
  title: string;
  description: string;
  action_type: string;
  metadata?: Record<string, unknown>;
}

// Service class for insights
export class InsightsService {
  /**
   * Get insights for an activity
   * @param activity The activity to get insights for
   * @returns Array of insights related to the activity
   */
  async getInsightsForActivity(activity: Activity): Promise<Insight[]> {
    try {
      // First, get the priority from the ML model
      const priority = await activityPriorityService.predictPriority(activity);

      // Only fetch insights for medium and high priority activities
      if (priority === PriorityLevel.LOW) {
        return [];
      }

      // Query the insights table for insights related to this activity
      const { data, error } = await supabase
        .schema('tenants')
        .from('insights')
        .select('*')
        .eq('tenant_id', activity.tenant_id)
        .contains('source_activities', [activity.id])
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching insights:', error);
        return [];
      }

      return data as Insight[];
    } catch (error) {
      console.error('Error in getInsightsForActivity:', error);
      return [];
    }
  }

  /**
   * Get insights for a case
   * @param caseId The case ID to get insights for
   * @param tenantId The tenant ID
   * @returns Array of insights related to the case
   */
  async getInsightsForCase(caseId: string, tenantId: string): Promise<Insight[]> {
    try {
      // Query the insights table for insights related to this case
      const { data, error } = await supabase
        .schema('tenants')
        .from('insights')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('metadata->case_id', caseId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching case insights:', error);
        return [];
      }

      return data as Insight[];
    } catch (error) {
      console.error('Error in getInsightsForCase:', error);
      return [];
    }
  }

  /**
   * Get insights for a document
   * @param documentId The document ID to get insights for
   * @param tenantId The tenant ID
   * @returns Array of insights related to the document
   */
  async getInsightsForDocument(documentId: string, tenantId: string): Promise<Insight[]> {
    try {
      // Query the insights table for insights related to this document
      const { data, error } = await supabase
        .schema('tenants')
        .from('insights')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('metadata->document_id', documentId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching document insights:', error);
        return [];
      }

      return data as Insight[];
    } catch (error) {
      console.error('Error in getInsightsForDocument:', error);
      return [];
    }
  }

  /**
   * Get all insights for a tenant
   * @param tenantId The tenant ID
   * @param limit Maximum number of insights to return
   * @returns Array of insights for the tenant
   */
  async getInsightsForTenant(tenantId: string, limit = 10): Promise<Insight[]> {
    try {
      // Query the insights table for all insights for this tenant
      const { data, error } = await supabase
        .schema('tenants')
        .from('insights')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching tenant insights:', error);
        return [];
      }

      return data as Insight[];
    } catch (error) {
      console.error('Error in getInsightsForTenant:', error);
      return [];
    }
  }

  /**
   * Get priority reasoning for an activity
   * This combines ML priority with insight context
   * @param activity The activity to get priority reasoning for
   * @returns Object with priority and reasoning
   */
  async getPriorityReasoning(activity: Activity): Promise<{
    priority: PriorityLevel;
    reasoning: string;
    features: any;
  }> {
    try {
      // Get priority from ML model
      const priority = await activityPriorityService.predictPriority(activity);

      // Extract features for explanation
      const features = (activity.metadata?.features || {}) as Partial<ActivityFeatures>;

      // Generate reasoning based on features
      let reasoning = "This activity was classified as ";

      switch (priority) {
        case PriorityLevel.HIGH:
          reasoning += "high priority because ";
          if (features.isUrgent) reasoning += "it is marked as urgent, ";
          if (features.isOverdue) reasoning += "it is overdue, ";
          if (features.isDueToday) reasoning += "it is due today, ";
          if (features.hasDeadline && typeof features.daysToDeadline === 'number' && features.daysToDeadline <= 3)
            reasoning += `it has a deadline in ${features.daysToDeadline} days, `;
          if (features.hasStatutoryLimit)
            reasoning += "it has statutory limitations, ";
          break;

        case PriorityLevel.MEDIUM:
          reasoning += "medium priority because ";
          if (features.isDueTomorrow) reasoning += "it is due tomorrow, ";
          if (features.hasDeadline && typeof features.daysToDeadline === 'number' && features.daysToDeadline <= 7)
            reasoning += `it has a deadline within ${features.daysToDeadline} days, `;
          if (features.daysSinceLastUpdate && features.daysSinceLastUpdate > 14)
            reasoning += `it hasn't been updated in ${features.daysSinceLastUpdate} days, `;
          break;

        case PriorityLevel.LOW:
          reasoning += "low priority because it doesn't have urgent deadlines or requirements. ";
          break;
      }

      // Clean up the reasoning
      reasoning = reasoning.replace(/, $/, ".");

      return {
        priority,
        reasoning,
        features
      };
    } catch (error) {
      console.error('Error in getPriorityReasoning:', error);
      return {
        priority: PriorityLevel.MEDIUM,
        reasoning: "Unable to determine priority reasoning due to an error.",
        features: {}
      };
    }
  }
}

// Create singleton instance
export const insightsService = new InsightsService();

// Export types
export { PriorityLevel };
