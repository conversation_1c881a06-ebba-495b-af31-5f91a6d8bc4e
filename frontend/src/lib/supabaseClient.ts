/**
 * Supabase Client Boilerplate
 * 
 * This file provides standardized Supabase client creation for both browser and server environments.
 * It supports the unified session helper pattern for gradual migration to Supabase Auth.
 */

import { createBrowserClient } from '@supabase/ssr';
import type { Database } from '@/lib/supabase/database.types';

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

/**
 * Create a Supabase client for browser use
 * This is used in client-side components and hooks
 */
export function createBrowserSupabaseClient() {
  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
}



// Export singleton browser client for convenience
let browserClient: ReturnType<typeof createBrowserSupabaseClient> | null = null;

export function getBrowserSupabaseClient() {
  if (typeof window === 'undefined') {
    throw new Error('getBrowserSupabaseClient can only be called in browser environment');
  }

  if (!browserClient) {
    browserClient = createBrowserSupabaseClient();
  }

  return browserClient;
}
