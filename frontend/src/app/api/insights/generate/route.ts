// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { createClient } from '@/lib/supabase/server';
import { InsightWorkflow } from '@/lib/langgraph/workflows/insight-workflow';
import { SwarmAgentSystem } from '@/lib/langgraph/agents/swarm';
import { SupervisorAgent } from '@/lib/langgraph/agents/supervisor';
import { EntityType, InsightSource } from '@/lib/langgraph/types/insight-graph';
import { logActivity } from '@/lib/activity-logger';
import { recordInsightInNeo4j } from '@/lib/neo4j/insights';
import { rateLimit } from '@/lib/rate-limit';
import { Activity, Insight } from '@/lib/types';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Define a local AuthUser type that matches what verifyJWT returns
interface AuthUser {
  id: string;
  email?: string;
  role?: string;
  tenantId?: string;
  metadata?: Record<string, any>;
}

// Define EntityType enum values for use as values (not just types)
const EntityTypes = {
  Case: 'case' as EntityType,
  Document: 'document' as EntityType,
  Deadline: 'deadline' as EntityType,
  Task: 'task' as EntityType,
  Client: 'client' as EntityType,
  Contact: 'contact' as EntityType
};

// Define types for the request body
interface GenerateInsightsRequest {
  source: InsightSource;
  entityId?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
}

// Entity data is defined inline in the generateInsights function

// Maximum number of activities to process
const MAX_ACTIVITIES = 100;

/**
 * Generate insights on demand
 *
 * POST /api/insights/generate
 * Body: {
 *   source: "activity" | "case" | "document" | "deadline",
 *   entityId?: string, // Optional entity ID, required if source is not "activity"
 *   startDate?: string, // Optional date range start
 *   endDate?: string, // Optional date range end
 *   limit?: number // Optional limit on activities to process
 * }
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Verify authentication
    const authResult = await verifyJWT(req);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Rate limiting - max 10 requests per minute
    const rateLimitResult = await rateLimit('generate_insights', 10, 60, authResult.user.id);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded', retryAfter: rateLimitResult.retryAfter },
        { status: 429, headers: { 'Retry-After': String(rateLimitResult.retryAfter) } }
      );
    }

    // Parse request body
    const { source, entityId, startDate, endDate, limit = MAX_ACTIVITIES }: GenerateInsightsRequest = await req.json();

    // Validate request
    if (!source) {
      return NextResponse.json({ error: 'Missing required field: source' }, { status: 400 });
    }

    if (source !== 'activity' && !entityId) {
      return NextResponse.json({ error: 'Entity ID is required for non-activity sources' }, { status: 400 });
    }

    // Initialize Supabase client
    const supabase = createClient();

    // Enhance the client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get relevant activities
    let activities = [];

    // Apply date filters if provided
    const dateFilters: any = {};
    if (startDate) {
      dateFilters.gte = startDate;
    }
    if (endDate) {
      dateFilters.lte = endDate;
    }

    // Case-specific activities
    if (source === 'case' && entityId) {
      // Get activities related to this case
      const { data, error } = await supabase
        .from('activities' as any)
        .select('*')
        .eq('case_id', entityId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      activities = data || [];

      // Get case details
      const { data: caseData, error: caseError } = await enhancedClient.tenants
        .from('cases')
        .select('*')
        .eq('id', entityId)
        .single();

      if (caseError) throw caseError;

      // Set up entity info
      const relatedEntity = {
        type: EntityTypes.Case,
        id: entityId,
        name: caseData?.name || 'Unknown Case',
        data: caseData
      };

      // Generate insights using the LangGraph workflow
      const insights = await generateInsights(activities as unknown as Activity[], 'case', relatedEntity, authResult.user);

      // Log this insight generation activity
      await logActivity({
        userId: authResult.user.id,
        type: 'generate_insights',
        description: `Generated insights for case ${relatedEntity.name}`,
        metadata: {
          source,
          entityId,
          insightCount: insights.length
        },
        caseId: entityId
      });

      return NextResponse.json({ insights, source, entityId });
    }

    // Document-specific activities
    else if (source === 'document' && entityId) {
      // Get activities related to this document
      const { data, error } = await supabase
        .schema('tenants')
        .from('activities')
        .select('*')
        .eq('document_id', entityId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      activities = data || [];

      // Get document details
      const { data: documentData, error: documentError } = await enhancedClient.tenants
        .from('documents')
        .select('*')
        .eq('id', entityId)
        .single();

      if (documentError) throw documentError;

      // Set up entity info
      const relatedEntity = {
        type: EntityTypes.Document,
        id: entityId,
        name: documentData?.name || 'Unknown Document',
        data: documentData
      };

      // Generate insights using the LangGraph workflow
      const insights = await generateInsights(activities as unknown as Activity[], 'document', relatedEntity, authResult.user);

      // Log this insight generation activity
      await logActivity({
        userId: authResult.user.id,
        type: 'generate_insights',
        description: `Generated insights for document ${relatedEntity.name}`,
        metadata: {
          source,
          entityId,
          insightCount: insights.length
        },
        documentId: entityId
      });

      return NextResponse.json({ insights, source, entityId });
    }

    // Deadline-specific activities
    else if (source === 'deadline' && entityId) {
      // Get activities related to this deadline
      const { data, error } = await supabase
        .schema('tenants')
        .from('activities')
        .select('*')
        .eq('deadline_id', entityId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      activities = data || [];

      // Get deadline details
      const { data: deadlineData, error: deadlineError } = await enhancedClient.tenants
        .from('deadlines')
        .select('*')
        .eq('id', entityId)
        .single();

      if (deadlineError) throw deadlineError;

      // Set up entity info
      const relatedEntity = {
        type: EntityTypes.Deadline,
        id: entityId,
        name: deadlineData?.title || 'Unknown Deadline',
        data: deadlineData
      };

      // Generate insights using the LangGraph workflow
      const insights = await generateInsights(activities as unknown as Activity[], 'deadline', relatedEntity, authResult.user);

      // Log this insight generation activity
      await logActivity({
        userId: authResult.user.id,
        type: 'generate_insights',
        description: `Generated insights for deadline ${relatedEntity.name}`,
        metadata: {
          source,
          entityId,
          insightCount: insights.length
        }
      });

      return NextResponse.json({ insights, source, entityId });
    }

    // General activity insights (not tied to a specific entity)
    else if (source === 'activity') {
      // Get recent activities
      const { data, error } = await supabase
        .schema('tenants')
        .from('activities')
        .select('*')
        .eq('user_id', authResult.user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      activities = data || [];

      // Generate insights using the LangGraph workflow
      const insights = await generateInsights(activities as unknown as Activity[], 'activity', null, authResult.user);

      // Log this insight generation activity
      await logActivity({
        userId: authResult.user.id,
        type: 'generate_insights',
        description: `Generated general activity insights`,
        metadata: {
          source,
          activityCount: activities.length,
          insightCount: insights.length
        }
      });

      return NextResponse.json({ insights, source });
    }

    // Invalid source
    else {
      return NextResponse.json({ error: 'Invalid source type' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error generating insights:', error);
    return NextResponse.json({ error: 'Failed to generate insights' }, { status: 500 });
  }
}

/**
 * Helper function to run the insight generation workflow
 */
async function generateInsights(
  activities: Activity[],
  source: InsightSource,
  relatedEntity: { type: EntityType; id: string; name: string; data: Record<string, unknown> } | null,
  user: AuthUser
): Promise<Insight[]> {
  try {
    // Create the agent instances
    const swarmAgentSystem = new SwarmAgentSystem();
    const supervisorAgent = new SupervisorAgent({
      userId: user.id,
      tenantId: user.tenantId || 'default',
      timestamp: new Date().toISOString(),
      userPreferences: user.metadata?.preferences || {},
      feedbackHistory: [], // In the future, fetch feedback history from database
    });

    // Create and run the workflow
    // @ts-ignore - Ignoring type errors for now as the InsightWorkflow constructor might have changed
    const workflow = new InsightWorkflow(supervisorAgent, swarmAgentSystem);

    // Type assertion for workflow.execute since TypeScript doesn't recognize it
    const execute = (workflow as any).execute as (params: {
      activities: Activity[];
      source: InsightSource;
      relatedEntity: any;
      startTime: string;
    }) => Promise<Insight[]>;

    const insights = await execute({
      activities,
      source,
      relatedEntity,
      startTime: new Date().toISOString(),
    });

    // Store insights in Neo4j for future reference (if enabled)
    try {
      for (const insight of insights) {
        await recordInsightInNeo4j(insight, user.id, source, relatedEntity);
      }
    } catch (neo4jError) {
      console.error('Error storing insights in Neo4j:', neo4jError);
      // Continue even if Neo4j storage fails
    }

    return insights;
  } catch (error) {
    console.error('Error in insight generation workflow:', error);
    throw error;
  }
}
