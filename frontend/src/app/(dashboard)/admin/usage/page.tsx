import React from 'react';
import { createClient } from '@/lib/auth/server-exports';
import { Database } from '@/lib/supabase/database.types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import UsageOverviewDashboard from '@/components/admin/usage/usage-overview-dashboard';
import TokenUsageDashboard from '@/components/admin/usage/token-usage-dashboard';
import VoiceAgentUsageDashboard from '@/components/admin/usage/voice-agent-usage-dashboard';

export const metadata = {
  title: 'Usage Monitoring | Admin Dashboard',
  description: 'Monitor resource usage across the platform',
};

export default async function UsagePage() {
  const supabase = await createClient();

  // Get aggregated usage data
  const { data: usageData } = await supabase
    .schema('tenants')
    .from('resource_usage')
    .select('usage_type, sum(usage_count), sum(resource_size_bytes)');

  // Get token usage data
  const { data: tokenUsageData } = await supabase
    .schema('tenants')
    .from('resource_usage')
    .select('*')
    .like('usage_type', 'ai_tokens%')
    .order('created_at', { ascending: false })
    .limit(10);

  // Get voice agent usage data
  const { data: voiceAgentUsageData } = await supabase
    .schema('tenants')
    .from('resource_usage')
    .select('*')
    .like('usage_type', 'voice_agent%')
    .order('created_at', { ascending: false })
    .limit(10);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Usage Monitoring</h1>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tokens">AI Tokens</TabsTrigger>
          <TabsTrigger value="voice-agents">Voice Agents</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Overview</CardTitle>
              <CardDescription>
                Monitor resource usage across all tenants
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UsageOverviewDashboard initialUsageData={
                usageData ? usageData.map(item => ({
                  usage_type: item.usage_type,
                  sum: Number(item.sum || 0)
                })) : []
              } />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI Token Usage</CardTitle>
              <CardDescription>
                Monitor AI token consumption
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TokenUsageDashboard initialTokenUsageData={tokenUsageData || []} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="voice-agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Voice Agent Usage</CardTitle>
              <CardDescription>
                Monitor voice agent usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VoiceAgentUsageDashboard initialVoiceAgentUsageData={voiceAgentUsageData || []} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
