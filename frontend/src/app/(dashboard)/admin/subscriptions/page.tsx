import React from 'react';
import { createClient } from '@/lib/auth/server-exports';
import { Database } from '@/lib/supabase/database.types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import SubscriptionPlansTable from '@/components/admin/subscription/subscription-plans-table';
import SubscriptionAddonsTable from '@/components/admin/subscription/subscription-addons-table';
import TenantSubscriptionsTable from '@/components/admin/subscription/tenant-subscriptions-table';

export const metadata = {
  title: 'Subscription Management | Admin Dashboard',
  description: 'Manage subscription plans, addons, and tenant subscriptions',
};

export default async function SubscriptionsPage() {
  const supabase = await createClient();

  // Get subscription plans
  const { data: plans } = await supabase
    .schema('tenants')
    .from('subscription_plans')
    .select('*')
    .order('base_price_monthly', { ascending: true });

  // Get subscription addons
  const { data: addons } = await supabase
    .schema('tenants')
    .from('subscription_addons')
    .select('*')
    .order('price_monthly', { ascending: true });

  // Get tenant subscriptions (limited to 10 most recent)
  const { data: tenantSubscriptions } = await supabase
    .schema('tenants')
    .from('tenant_subscriptions')
    .select(`
      *,
      subscription_plans!inner (
        name, code, base_price_monthly, base_price_yearly, features
      ),
      firms!inner (
        name, tenant_id
      )
    `)
    .order('created_at', { ascending: false })
    .limit(10);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
      </div>

      <Tabs defaultValue="tenant-subscriptions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tenant-subscriptions">Tenant Subscriptions</TabsTrigger>
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
          <TabsTrigger value="addons">Subscription Addons</TabsTrigger>
        </TabsList>

        <TabsContent value="tenant-subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tenant Subscriptions</CardTitle>
              <CardDescription>
                View and manage tenant subscriptions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TenantSubscriptionsTable initialSubscriptions={tenantSubscriptions || []} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Plans</CardTitle>
              <CardDescription>
                Manage available subscription plans
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SubscriptionPlansTable initialPlans={plans || []} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addons" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Addons</CardTitle>
              <CardDescription>
                Manage available subscription addons
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SubscriptionAddonsTable initialAddons={addons || []} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
